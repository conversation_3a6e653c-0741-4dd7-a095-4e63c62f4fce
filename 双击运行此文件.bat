@echo off
setlocal

:: Force the current directory to the batch file's directory
:: This is critical for "Run as administrator"
cd /d "%~dp0"

set "PYTHON_SCRIPT=fix_all_modules.py"

echo ==========================================================
echo       Universal Module Fixer (Admin & Direct Mode)
echo ==========================================================
echo.
echo Running in directory: %CD%
echo.

:: Set code page to handle Python's output, though prompts are in English
chcp 65001 > nul

echo [Check 1] Checking for Python environment...
python --version
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Python not found in your system!
    goto :end_script
)
echo [SUCCESS] Python environment found.
echo.

echo [Check 2] Checking for core script file: "%PYTHON_SCRIPT%"...
if not exist "%PYTHON_SCRIPT%" (
    echo.
    echo [ERROR] Core script file not found: "%PYTHON_SCRIPT%"!
    goto :end_script
)
echo [SUCCESS] Core script file found.
echo.

echo Ready to call Python script for the fix...
echo.
echo --- Python Script Output Start ---
python "%PYTHON_SCRIPT%"
echo --- Python Script Output End ---
echo.
echo [SUCCESS] Script execution finished.

:end_script
echo.
echo ==========================================================
echo Run finished. Please send a screenshot of this entire window if issues persist.
echo.
echo Press any key to exit...
pause 