import json
import os
import shutil
import glob

def fix_module_file(module_filename, reverse_map):
    """
    修复单个模块文件
    :param module_filename: 要修复的模块文件名
    :param reverse_map: 英文原文到i18n键的映射
    """
    print(f"\n--- 正在处理: {module_filename} ---")

    if not os.path.exists(module_filename):
        print(f"信息：未找到文件 '{module_filename}'，将跳过。")
        return

    # 1. 备份原始模块文件
    backup_file = f"{module_filename}.bak"
    try:
        shutil.copy(module_filename, backup_file)
        print(f"已成功备份原始文件为 '{backup_file}'")
    except Exception as e:
        print(f"错误：备份文件 '{module_filename}' 时失败: {e}")
        return

    # 2. 读取并修复模块文件
    try:
        with open(module_filename, 'r', encoding='utf-8') as f:
            module_data = json.load(f)
        
        modified_count = 0
        total_pairs = 0

        for section in module_data:
            if "pairs" in section and isinstance(section["pairs"], list):
                for pair in section["pairs"]:
                    total_pairs += 1
                    key = pair.get("key")
                    value = pair.get("value")
                    if key in reverse_map and key == value:
                        pair["value"] = reverse_map[key]
                        modified_count += 1
        
        print(f"扫描完成。在 {total_pairs} 个条目中，成功修复了 {modified_count} 个。")

        # 3. 如果有修改，则写回文件
        if modified_count > 0:
            with open(module_filename, 'w', encoding='utf-8') as f:
                json.dump(module_data, f, ensure_ascii=False, indent=4)
            print(f"已成功将修复后的内容写入 '{module_filename}'。")
        else:
            print("文件内容正确，无需修改。")

    except Exception as e:
        print(f"错误：处理模块文件 '{module_filename}' 时失败: {e}")

def run_fix():
    """
    主修复逻辑函数
    """
    print("--- 通用汉化模块修复脚本 ---")

    # --- 文件配置 ---
    EN_US_FILE = "en_us.json"
    ZH_CN_FILE = "zh_cn.json"

    # 1. 检查语言文件是否存在
    required_files = [EN_US_FILE, ZH_CN_FILE]
    for filename in required_files:
        if not os.path.exists(filename):
            print(f"错误：未找到必需的语言文件 '{filename}'！")
            print(f"请确保此脚本与 {EN_US_FILE} 和 {ZH_CN_FILE} 放在同一个文件夹下。")
            return

    print("成功找到必需的语言文件。")

    # 2. 创建英文原文到 i18n 键的映射 (一次性)
    print(f"正在读取 '{EN_US_FILE}' 以创建翻译映射...")
    try:
        with open(EN_US_FILE, 'r', encoding='utf-8') as f:
            en_data = json.load(f)
        
        reverse_map = {v: k for k, v in en_data.items()}
        print(f"翻译映射创建成功，共 {len(reverse_map)} 个条目。")
    except Exception as e:
        print(f"错误：读取或解析 '{EN_US_FILE}' 失败: {e}")
        return

    # 3. 动态查找所有需要修复的 .json 文件
    print("\n正在扫描当前文件夹下的模块文件...")
    all_json_files = glob.glob('*.json')
    
    # 从列表中排除语言文件
    files_to_exclude = {EN_US_FILE, ZH_CN_FILE}
    module_files_to_fix = [f for f in all_json_files if f not in files_to_exclude]

    if not module_files_to_fix:
        print("未找到任何需要修复的模块文件 (.json)。")
        print("请确保模块文件与本脚本在同一个文件夹下。")
        return
        
    print(f"找到 {len(module_files_to_fix)} 个可能需要修复的模块文件: {', '.join(module_files_to_fix)}")

    # 4. 循环处理每一个找到的模块文件
    for module_file in module_files_to_fix:
        fix_module_file(module_file, reverse_map)

    print("\n--- 所有操作完成！---")

if __name__ == "__main__":
    run_fix() 